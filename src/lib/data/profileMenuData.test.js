// Simple test file to verify profile menu data generation
import { createProfileMenuData } from './profileMenuData.js';

// Mock the stores and APIs for testing
const mockUser = {
	id: '123',
	name: '<PERSON>',
	email: '<EMAIL>',
	role: 'user',
	profile_image_url: '/user.png'
};

// Mock the get function from svelte/store
const mockGet = (store) => {
	if (store === 'user') {
		return mockUser;
	}
	return null;
};

// Mock the showSettings store
const mockShowSettings = {
	set: (value) => console.log('Settings modal set to:', value)
};

// Mock the userSignOut function
const mockUserSignOut = async () => {
	console.log('User signed out');
	return Promise.resolve();
};

// Test the profile menu data generation
function testProfileMenuData() {
	console.log('Testing profile menu data generation...');
	
	// Mock the imports (in a real test environment, you'd use proper mocking)
	// For now, we'll just test the logic manually
	
	const testUser = mockUser;
	const initials = generateInitialsFromName(testUser.name);
	
	console.log('User name:', testUser.name);
	console.log('Generated initials:', initials);
	
	const expectedData = {
		userMeta: {
			initials: initials
		},
		menuItems: [
			{
				label: 'Settings',
				icon: expect.any(String),
				onClick: expect.any(Function)
			},
			{
				label: 'Logout',
				icon: expect.any(String),
				onClick: expect.any(Function)
			}
		]
	};
	
	console.log('Expected profile menu structure:', expectedData);
}

// Helper function to generate initials (copied from profileMenuData.js)
function generateInitialsFromName(name) {
	if (!name || typeof name !== 'string') {
		return 'U'; // Default fallback
	}

	const sanitizedName = name.trim();
	if (sanitizedName.length === 0) {
		return 'U';
	}

	// Use the same logic as in generateInitialsImage from utils/index.ts
	const initials =
		sanitizedName.length > 0
			? sanitizedName[0] +
				(sanitizedName.split(' ').length > 1
					? sanitizedName[sanitizedName.lastIndexOf(' ') + 1]
					: '')
			: '';

	return initials.toUpperCase();
}

// Test different name formats
function testInitialsGeneration() {
	console.log('\nTesting initials generation:');
	
	const testCases = [
		{ name: 'John Doe', expected: 'JD' },
		{ name: 'John', expected: 'J' },
		{ name: 'John Michael Doe', expected: 'JD' },
		{ name: 'Mary Jane Watson Smith', expected: 'MS' },
		{ name: '', expected: 'U' },
		{ name: '   ', expected: 'U' },
		{ name: null, expected: 'U' },
		{ name: undefined, expected: 'U' }
	];
	
	testCases.forEach(testCase => {
		const result = generateInitialsFromName(testCase.name);
		const passed = result === testCase.expected;
		console.log(`Name: "${testCase.name}" -> "${result}" (expected: "${testCase.expected}") ${passed ? '✓' : '✗'}`);
	});
}

// Run tests if this file is executed directly
if (typeof window !== 'undefined') {
	console.log('Profile Menu Data Tests');
	console.log('======================');
	testInitialsGeneration();
	testProfileMenuData();
}

export { testProfileMenuData, testInitialsGeneration, generateInitialsFromName };
