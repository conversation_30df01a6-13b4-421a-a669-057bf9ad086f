import { get } from 'svelte/store';
import { user, showSettings } from '$lib/stores';
import { userSignOut } from '$lib/apis/auths';

/**
 * Creates profile menu data for Graymatter UI components
 * @returns {Object} Profile menu data object with userMeta and menuItems
 */
export function createProfileMenuData() {
	const currentUser = get(user);

	if (!currentUser) {
		return null;
	}

	// Generate user initials from name
	const initials = generateInitialsFromName(currentUser.name);

	return {
		userMeta: {
			initials: initials
		},
		menuItems: [
			{
				label: 'Settings',
				icon: '/icons/settings.svg',
				onClick: () => {
					showSettings.set(true);
				}
			},
			{
				label: 'Logout',
				icon: '/icons/logout.svg',
				onClick: async () => {
					try {
						// Sign out from the backend
						await userSignOut().catch((e) => console.log('Error signing out: ', e));

						// Clear local storage
						localStorage.removeItem('token');

						// Redirect to auth page
						window.location.href = '/auth';
					} catch (error) {
						console.error('Error during logout:', error);
						// Still redirect even if there's an error
						window.location.href = '/auth';
					}
				}
			}
		]
	};
}

/**
 * Helper function to generate initials from a name
 * Uses the same logic as generateInitialsImage from utils
 * @param {string} name - The user's full name
 * @returns {string} User initials (up to 2 characters)
 */
function generateInitialsFromName(name) {
	if (!name || typeof name !== 'string') {
		return 'U'; // Default fallback
	}

	const sanitizedName = name.trim();
	if (sanitizedName.length === 0) {
		return 'U';
	}

	// Use the same logic as in generateInitialsImage from utils/index.ts
	const initials =
		sanitizedName.length > 0
			? sanitizedName[0] +
			(sanitizedName.split(' ').length > 1
				? sanitizedName[sanitizedName.lastIndexOf(' ') + 1]
				: '')
			: '';

	return initials.toUpperCase();
}
