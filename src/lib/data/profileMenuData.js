import { get } from 'svelte/store';
import { user, showSettings } from '$lib/stores';
import { userSignOut } from '$lib/apis/auths';
import { generateInitials } from '$lib/utils';

/**
 * Creates profile menu data for Graymatter UI components
 * @returns {Object} Profile menu data object with userMeta and menuItems
 */
export function createProfileMenuData() {
	const currentUser = get(user);

	if (!currentUser) {
		return null;
	}

	// Generate user initials from name using the existing utility function
	const initials = generateInitials(currentUser.name);

	return {
		userMeta: {
			initials: initials
		},
		menuItems: [
			{
				label: 'Settings',
				icon: '/icons/settings.svg',
				onClick: () => {
					showSettings.set(true);
				}
			},
			{
				label: 'Logout',
				icon: '/icons/logout.svg',
				onClick: async () => {
					try {
						// Sign out from the backend
						await userSignOut().catch((e) => console.log('Error signing out: ', e));

						// Clear local storage
						localStorage.removeItem('token');

						// Redirect to auth page
						window.location.href = '/auth';
					} catch (error) {
						console.error('Error during logout:', error);
						// Still redirect even if there's an error
						window.location.href = '/auth';
					}
				}
			}
		]
	};
}


