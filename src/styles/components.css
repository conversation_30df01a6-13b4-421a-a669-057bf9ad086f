/* Chat input container and messages */
#chat-input p[data-placeholder]::before,
#chat-input p[data-placeholder]::after {
	color: var(--ai-color-neutral-500);
}
.chat-input-container {
	border-color: var(--ai-color-steel-400);
}
.chat-input-placeholder {
	color: var(--ai-color-neutral-500);
}
.chat-input-helper-text,
.chat-message-time {
	color: var(--ai-color-steel-800);
}
.chat-input-welcome-msg {
	color: var(--ai-color-steel-900);
}
.chat-input-upload-btn,
.chat-message-user {
	background-color: var(--ai-color-steel-100);
	color: var(--ai-color-black);
}
.chat-input-upload-btn:hover,
.sidebar-text-link:hover,
.sidebar-text-link:active {
	background-color: var(--ai-color-steel-200);
}
.chat-name-text,
.chat-text,
.chat-model-selector,
#chat-input p {
	color: var(--ai-color-black);
}
.chat-send-msg-btn {
	background-color: var(--ai-color-black);
	color: var(--ai-color-white);
}
.chat-send-msg-btn:hover {
	background-color: var(--ai-color-neutral-900);
}
.chat-input-upload-btn {
	padding: var(--ai-size-6);
}

.chat-text,
.chat-message-user {
	line-height: var(--ai-font-lineheight-170);
}

.chat-suggested-prompts {
	background-color: transparent;
	border: 1px solid var(--ai-color-steel-400);
	color: var(--ai-color-steel-800);
	font-size: var(--ai-size-14);
	align-items: center;
	padding: var(--ai-size-8);
}
.chat-suggested-prompts:hover {
	color: var(--ai-color-black);
	border: 1px solid var(--ai-color-steel-500);
	box-shadow: 0px 2px 5px 0px rgba(50, 54, 61, 0.25);
	transition: all 0.5s ease;
}
/* Code block */
.codeblock {
	background-color: var(--ai-color-steel-50);
	border: 1px solid var(--ai-color-steel-200);
	font-size: 13px;
}
.codeblock-header {
	background-color: var(--ai-color-steel-50);
	border-bottom: 1px solid var(--ai-color-steel-200);
	border-top-left-radius: var(--ai-size-8);
	border-top-right-radius: var(--ai-size-8);
}
.codeblock .codeblock-header-content {
	padding-top: var(--ai-size-28);
}
.codeblock .codeblock-lang,
.cm-content {
	font-family: 'Space Mono';
}
.codeblock .codeblock-lang {
	font-size: 13px;
	line-height: var(--ai-font-lineheight-130);
}
.codeblock-lang {
	padding-left: var(--ai-size-16);
	padding-top: var(--ai-size-6);
	padding-bottom: var(--ai-size-6);
}
.codeblock .copy-code-button {
	display: flex;
	background-image: none;
	background-color: transparent;
	border-style: none;
	align-items: center;
	font-size: var(--ai-size-14);
	color: var(--ai-color-steel-800);
}
.codeblock .copy-code-button:hover {
	background-color: var(--ai-color-steel-200);
}
.ͼ2 .cm-gutters {
	background-color: var(--ai-color-steel-50);
}
/* Sidebar */
.sidebar-container {
	background-color: var(--ai-color-steel-100);
}
.sidebar-text,
.sidebar-text-link,
.sidebar-main-label {
	color: var(--ai-color-steel-900);
}

.chat-message-item {
	width: 100%;
	display: flex;
	justify-content: space-between;
	border-radius: var(--ai-radius-small);
	padding: var(--ai-size-8);
	color: var(--ai-color-steel-800);
	font-size: var(--ai-size-14);
}
.chat-message-name {
	text-align: left;
	align-self: center;
	overflow: hidden;
	width: 100%;
	height: 20px;
	text-overflow: ellipsis;
}

.sidebar-new-chat-btn {
	color: var(--ai-color-steel-900);
	font-size: var(--ai-size-16);
	font-weight: var(--ai-font-weight-600);
	display: flex;
	align-items: center;
	border-radius: var(--ai-radius-small);
	padding: var(--ai-size-8) var(--ai-size-8) var(--ai-size-8) var(--ai-size-4);
	margin: 0 var(--ai-size-8);
	height: 100%;
	gap: 6px;
}
.sidebar-new-chat-btn:hover {
	background-color: var(--ai-color-steel-200);
}

graymatter-desktop-side-nav::part(nav-content) {
	padding-bottom: 0;
}

graymatter-desktop-side-nav::part(nav-footer) {
	padding-bottom: var(--ai-size-24);
}
graymatter-desktop-side-nav::part(icon-container),
graymatter-desktop-side-nav::part(chat-icon-container),
graymatter-desktop-side-nav::part(console-icon-container),
graymatter-desktop-side-nav::part(api-icon-container) {
	box-sizing: border-box;
}

graymatter-desktop-side-nav::part(discover-item) {
	display: none;
}

.chat-content-wrapper {
	/* height: calc(100vh - 110px); */
	flex-direction: column;
	display: flex;
	position: relative;
	overflow-x: hidden;
	overflow-y: auto;
}

/* Chat header */
.chat-console-header {
	background-color: var(--ai-color-white);
	padding: var(--ai-size-6);
	display: flex;
	align-items: center;
	width: 100%;
	position: sticky;
	top: 0px;
	z-index: 30;
	margin-bottom: -2rem;
}

.navbar-wrapper.sidebar-shifted {
	margin-left: 100px;
}

/* Chat links styling */
.chat-link-small,
.chat-link-regular,
.chat-text a,
.chat-message-user a {
	font-weight: var(--ai-font-weight-400);
	color: var(--ai-color-black);
	padding: 0px 0px 4px;
	border-bottom: 1px solid var(--ai-color-black);
	text-decoration: none;
}

.chat-link-small {
	font-size: var(--ai-size-14);
}

.chat-link-regular {
	font-size: var(--ai-size-16);
}

.chat-link-small,
.chat-link-regular,
.chat-text a,
.chat-message-user a {
	/* Setting border-width prevents the button from "moving" when the state changes. */
	outline-width: 2px;
	outline-color: transparent;
}

.chat-link-small:hover,
.chat-link-small:active,
.chat-link-regular:hover,
.chat-link-regular:active,
.chat-text a:hover,
.chat-text a:active,
.chat-message-user a:hover,
.chat-message-user a:active {
	color: var(--ai-color-neutral-700);
	border-color: var(--ai-color-neutral-700);
}

.chat-link-small:focus,
.chat-link-regular:focus,
.chat-text a:focus,
.chat-message-user a:focus {
	outline: 2px solid var(--ai-color-blue-600);
}
.model-selector-item button,
.temporary-chat {
	/* Necessary to apply state styling. */
	position: relative;

	/* State=Default */
	padding: var(--ai-size-8);
	border-radius: var(--ai-radius-small);

	/* Text style */
	font-weight: var(--ai-font-weight-400);
	font-size: var(--ai-size-14);
	line-height: var(--ai-font-lineheight-130);
	color: var(--ai-color-black);

	/* Setting border-width prevents the button from "moving" when the state changes. */
	border-width: 2px;
	border-color: transparent;
}

.model-selector-item button:hover,
.model-selector-item button:active,
.model-selector-item button:focus,
.temporary-chat:hover,
.temporary-chat:active,
.temporary-chat:focus {
	/* State = hover, active, or focus */
	background-color: var(--ai-color-steel-200);

	/* Text style */
	color: var(--ai-color-black);
}

.model-selector-item button:focus,
.temporary-chat:focus {
	/* State = focus */
	border: 2px solid var(--ai-color-blue-600) !important;
}
